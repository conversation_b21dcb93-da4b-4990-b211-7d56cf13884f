<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\View;
use App\Models\Category;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Use Bootstrap for pagination
        Paginator::useBootstrapFive();

        // Share categories with all views
        View::composer('*', function ($view) {
            $categories = Category::active()
                ->parent()
                ->orderBy('sort_order')
                ->take(10)
                ->get();
            
            $view->with('globalCategories', $categories);
        });
    }
}
