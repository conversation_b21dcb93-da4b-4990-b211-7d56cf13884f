<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    /**
     * Display search results.
     */
    public function index(Request $request)
    {
        $query = $request->get('q');
        
        if (empty($query)) {
            return redirect()->route('products.index');
        }

        $products = Product::with(['category', 'supplier', 'reviews'])
            ->active()
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('name_ar', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('description_ar', 'like', "%{$query}%")
                  ->orWhere('sku', 'like', "%{$query}%")
                  ->orWhere('brand', 'like', "%{$query}%")
                  ->orWhereHas('category', function ($categoryQuery) use ($query) {
                      $categoryQuery->where('name', 'like', "%{$query}%")
                                   ->orWhere('name_ar', 'like', "%{$query}%");
                  });
            })
            ->paginate(12)
            ->withQueryString();

        // Get related categories
        $categories = Category::active()
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('name_ar', 'like', "%{$query}%");
            })
            ->take(5)
            ->get();

        return view('search.results', compact('products', 'categories', 'query'));
    }

    /**
     * Get search suggestions for autocomplete.
     */
    public function suggestions(Request $request)
    {
        $query = $request->get('q');
        
        if (strlen($query) < 2) {
            return response()->json([]);
        }

        // Get product suggestions
        $productSuggestions = Product::active()
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('name_ar', 'like', "%{$query}%");
            })
            ->select('name', 'name_ar', 'slug')
            ->take(5)
            ->get()
            ->map(function ($product) {
                return [
                    'name' => app()->getLocale() == 'ar' ? ($product->name_ar ?? $product->name) : $product->name,
                    'type' => 'product',
                    'url' => route('product.show', $product->slug)
                ];
            });

        // Get category suggestions
        $categorySuggestions = Category::active()
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('name_ar', 'like', "%{$query}%");
            })
            ->select('name', 'name_ar', 'slug')
            ->take(3)
            ->get()
            ->map(function ($category) {
                return [
                    'name' => app()->getLocale() == 'ar' ? ($category->name_ar ?? $category->name) : $category->name,
                    'type' => 'category',
                    'url' => route('category.show', $category->slug)
                ];
            });

        $suggestions = $productSuggestions->concat($categorySuggestions)->take(8);

        return response()->json($suggestions);
    }
}
