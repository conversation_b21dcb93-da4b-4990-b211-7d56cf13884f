<?php
/**
 * Simple Laravel-like application runner
 * This creates a working B2B marketplace without requiring full Laravel installation
 */

// Start session
session_start();

// Simple routing
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);

// Remove query string and clean path
$path = strtok($path, '?');
$path = rtrim($path, '/');
if (empty($path)) $path = '/';

// Simple database simulation
class DB {
    public static $users = [
        ['id' => 1, 'name' => 'مدير النظام', 'email' => '<EMAIL>', 'role' => 'admin', 'password' => 'password'],
        ['id' => 2, 'name' => 'أحمد محمد', 'email' => '<EMAIL>', 'role' => 'supplier', 'password' => 'password'],
        ['id' => 3, 'name' => 'فاطمة علي', 'email' => '<EMAIL>', 'role' => 'retailer', 'password' => 'password'],
    ];
    
    public static $products = [
        ['id' => 1, 'name' => 'لابتوب Dell XPS 13', 'price' => 4500, 'wholesale_price' => 3800, 'category' => 'الإلكترونيات', 'stock' => 25, 'image' => 'laptop.jpg'],
        ['id' => 2, 'name' => 'هاتف Samsung Galaxy S23', 'price' => 3200, 'wholesale_price' => 2700, 'category' => 'الإلكترونيات', 'stock' => 15, 'image' => 'phone.jpg'],
        ['id' => 3, 'name' => 'ساعة Apple Watch Series 9', 'price' => 1800, 'wholesale_price' => 1500, 'category' => 'الإلكترونيات', 'stock' => 30, 'image' => 'watch.jpg'],
        ['id' => 4, 'name' => 'سماعات Sony WH-1000XM5', 'price' => 1200, 'wholesale_price' => 950, 'category' => 'الإلكترونيات', 'stock' => 20, 'image' => 'headphones.jpg'],
    ];
    
    public static $orders = [
        ['id' => 1, 'order_number' => 'ORD001234', 'user_id' => 2, 'total' => 15500, 'status' => 'pending', 'date' => '2024-01-15'],
        ['id' => 2, 'order_number' => 'ORD001233', 'user_id' => 3, 'total' => 8750, 'status' => 'processing', 'date' => '2024-01-15'],
        ['id' => 3, 'order_number' => 'ORD001232', 'user_id' => 2, 'total' => 22300, 'status' => 'shipped', 'date' => '2024-01-14'],
    ];
}

// Authentication helper
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function getCurrentUser() {
    if (!isLoggedIn()) return null;
    foreach (DB::$users as $user) {
        if ($user['id'] == $_SESSION['user_id']) {
            return $user;
        }
    }
    return null;
}

function requireAuth() {
    if (!isLoggedIn()) {
        header('Location: /login');
        exit;
    }
}

function requireAdmin() {
    requireAuth();
    $user = getCurrentUser();
    if ($user['role'] !== 'admin') {
        header('Location: /');
        exit;
    }
}

// Handle login
if ($path === '/login' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    foreach (DB::$users as $user) {
        if ($user['email'] === $email && $user['password'] === $password) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_role'] = $user['role'];
            
            if ($user['role'] === 'admin') {
                header('Location: /admin');
            } else {
                header('Location: /');
            }
            exit;
        }
    }
    $login_error = 'بيانات الدخول غير صحيحة';
}

// Handle logout
if ($path === '/logout') {
    session_destroy();
    header('Location: /');
    exit;
}

// Routing
switch ($path) {
    case '/':
        include 'views/home.php';
        break;
    case '/login':
        include 'views/login.php';
        break;
    case '/admin':
        requireAdmin();
        include 'views/admin/dashboard.php';
        break;
    case '/admin/products':
        requireAdmin();
        include 'views/admin/products.php';
        break;
    case '/admin/orders':
        requireAdmin();
        include 'views/admin/orders.php';
        break;
    case '/admin/users':
        requireAdmin();
        include 'views/admin/users.php';
        break;
    case '/products':
        include 'views/products.php';
        break;
    case '/api/products':
        header('Content-Type: application/json');
        echo json_encode(DB::$products);
        break;
    default:
        http_response_code(404);
        echo '<h1>404 - الصفحة غير موجودة</h1>';
        break;
}
?>
