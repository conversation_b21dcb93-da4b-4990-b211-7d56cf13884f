/* ===== Custom Variables ===== */
:root {
    --primary-color: #0A2463;
    --secondary-color: #FF6B35;
    --light-color: #FFFFFF;
    --dark-color: #1a1a1a;
    --gray-color: #6c757d;
    --light-gray: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* ===== Base Styles ===== */
* {
    font-family: 'Cairo', sans-serif;
}

body {
    background-color: var(--light-gray);
    transition: all 0.3s ease;
}

/* ===== Bootstrap Customization ===== */
.bg-primary {
    background-color: var(--primary-color) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #082052;
    border-color: #082052;
}

.text-warning, .bg-warning {
    color: var(--secondary-color) !important;
    background-color: var(--secondary-color) !important;
}

.btn-warning {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
}

.btn-warning:hover {
    background-color: #e55a2b;
    border-color: #e55a2b;
    color: white;
}

/* ===== Theme Styles ===== */
.light-theme {
    --bg-color: #ffffff;
    --text-color: #333333;
    --card-bg: #ffffff;
    --border-color: #dee2e6;
}

.dark-theme {
    --bg-color: #1a1a1a;
    --text-color: #ffffff;
    --card-bg: #2d2d2d;
    --border-color: #404040;
    background-color: var(--bg-color);
    color: var(--text-color);
}

.dark-theme .navbar-dark {
    background-color: #0d1b54 !important;
}

.dark-theme .card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

.dark-theme .bg-light {
    background-color: var(--card-bg) !important;
}

.dark-theme .text-dark {
    color: var(--text-color) !important;
}

.dark-theme .border {
    border-color: var(--border-color) !important;
}

/* ===== Custom Components ===== */
.product-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    box-shadow: var(--shadow);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.product-image {
    height: 200px;
    object-fit: cover;
    border-radius: 0.375rem 0.375rem 0 0;
}

.category-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: var(--shadow);
    text-decoration: none;
    color: inherit;
}

.category-card:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
    color: inherit;
    text-decoration: none;
}

.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1e3a8a 100%);
    min-height: 400px;
    display: flex;
    align-items: center;
}

.stats-card {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #ff8c42 100%);
    color: white;
    border: none;
    box-shadow: var(--shadow);
}

/* ===== Search Suggestions ===== */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;
    box-shadow: var(--shadow);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
}

.search-suggestion-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-suggestion-item:hover {
    background-color: var(--light-gray);
}

.search-suggestion-item:last-child {
    border-bottom: none;
}

/* ===== Price Display ===== */
.price-original {
    text-decoration: line-through;
    color: var(--gray-color);
    font-size: 0.9em;
}

.price-discounted {
    color: var(--secondary-color);
    font-weight: bold;
    font-size: 1.1em;
}

.discount-badge {
    background-color: var(--secondary-color);
    color: white;
    font-size: 0.8em;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
}

/* ===== Rating Stars ===== */
.rating-stars {
    color: #ffc107;
}

.rating-stars .far {
    color: #dee2e6;
}

/* ===== Loading Spinner ===== */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== Responsive Adjustments ===== */
@media (max-width: 768px) {
    .hero-section {
        min-height: 300px;
        text-align: center;
    }
    
    .product-card {
        margin-bottom: 1rem;
    }
    
    .search-form {
        width: 100%;
        margin: 1rem 0;
    }
}

/* ===== RTL Support ===== */
[dir="rtl"] .navbar-nav {
    text-align: right;
}

[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
}

/* ===== Animation Classes ===== */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* ===== Custom Scrollbar ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}
