@extends('layouts.app')

@section('title', 'إنشاء حساب جديد')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        إنشاء حساب جديد
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form method="POST" action="{{ route('register') }}">
                        @csrf

                        <div class="row">
                            <!-- Name -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">الاسم الكامل</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-user"></i>
                                    </span>
                                    <input id="name" 
                                           class="form-control @error('name') is-invalid @enderror" 
                                           type="text" 
                                           name="name" 
                                           value="{{ old('name') }}" 
                                           required 
                                           autofocus 
                                           autocomplete="name"
                                           placeholder="أدخل اسمك الكامل">
                                </div>
                                @error('name')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Email -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input id="email" 
                                           class="form-control @error('email') is-invalid @enderror" 
                                           type="email" 
                                           name="email" 
                                           value="{{ old('email') }}" 
                                           required 
                                           autocomplete="username"
                                           placeholder="أدخل بريدك الإلكتروني">
                                </div>
                                @error('email')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <!-- Phone -->
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-phone"></i>
                                    </span>
                                    <input id="phone" 
                                           class="form-control @error('phone') is-invalid @enderror" 
                                           type="tel" 
                                           name="phone" 
                                           value="{{ old('phone') }}" 
                                           required
                                           placeholder="05xxxxxxxx">
                                </div>
                                @error('phone')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Company Name -->
                            <div class="col-md-6 mb-3">
                                <label for="company_name" class="form-label">اسم الشركة</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-building"></i>
                                    </span>
                                    <input id="company_name" 
                                           class="form-control @error('company_name') is-invalid @enderror" 
                                           type="text" 
                                           name="company_name" 
                                           value="{{ old('company_name') }}" 
                                           required
                                           placeholder="أدخل اسم شركتك">
                                </div>
                                @error('company_name')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Business Type -->
                        <div class="mb-3">
                            <label for="business_type" class="form-label">نوع النشاط التجاري</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-industry"></i>
                                </span>
                                <select id="business_type" 
                                        class="form-select @error('business_type') is-invalid @enderror" 
                                        name="business_type" 
                                        required>
                                    <option value="">اختر نوع النشاط</option>
                                    <option value="manufacturer" {{ old('business_type') == 'manufacturer' ? 'selected' : '' }}>
                                        مصنع
                                    </option>
                                    <option value="supplier" {{ old('business_type') == 'supplier' ? 'selected' : '' }}>
                                        مورد
                                    </option>
                                    <option value="retailer" {{ old('business_type') == 'retailer' ? 'selected' : '' }}>
                                        تاجر تجزئة
                                    </option>
                                    <option value="distributor" {{ old('business_type') == 'distributor' ? 'selected' : '' }}>
                                        موزع
                                    </option>
                                </select>
                            </div>
                            @error('business_type')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <!-- City -->
                            <div class="col-md-6 mb-3">
                                <label for="city" class="form-label">المدينة</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </span>
                                    <input id="city" 
                                           class="form-control @error('city') is-invalid @enderror" 
                                           type="text" 
                                           name="city" 
                                           value="{{ old('city') }}"
                                           placeholder="أدخل المدينة">
                                </div>
                                @error('city')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Country -->
                            <div class="col-md-6 mb-3">
                                <label for="country" class="form-label">الدولة</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-globe"></i>
                                    </span>
                                    <input id="country" 
                                           class="form-control @error('country') is-invalid @enderror" 
                                           type="text" 
                                           name="country" 
                                           value="{{ old('country', 'المملكة العربية السعودية') }}"
                                           placeholder="أدخل الدولة">
                                </div>
                                @error('country')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Address -->
                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان (اختياري)</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-map"></i>
                                </span>
                                <textarea id="address" 
                                          class="form-control @error('address') is-invalid @enderror" 
                                          name="address" 
                                          rows="2"
                                          placeholder="أدخل العنوان التفصيلي">{{ old('address') }}</textarea>
                            </div>
                            @error('address')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <!-- Password -->
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input id="password" 
                                           class="form-control @error('password') is-invalid @enderror"
                                           type="password"
                                           name="password"
                                           required 
                                           autocomplete="new-password"
                                           placeholder="أدخل كلمة المرور">
                                </div>
                                @error('password')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Confirm Password -->
                            <div class="col-md-6 mb-3">
                                <label for="password_confirmation" class="form-label">تأكيد كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input id="password_confirmation" 
                                           class="form-control @error('password_confirmation') is-invalid @enderror"
                                           type="password"
                                           name="password_confirmation"
                                           required 
                                           autocomplete="new-password"
                                           placeholder="أعد إدخال كلمة المرور">
                                </div>
                                @error('password_confirmation')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Terms Agreement -->
                        <div class="mb-3 form-check">
                            <input id="terms" type="checkbox" class="form-check-input" required>
                            <label for="terms" class="form-check-label">
                                أوافق على 
                                <a href="{{ route('terms') }}" target="_blank" class="text-decoration-none">
                                    الشروط والأحكام
                                </a>
                                و
                                <a href="{{ route('privacy') }}" target="_blank" class="text-decoration-none">
                                    سياسة الخصوصية
                                </a>
                            </label>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>
                                إنشاء الحساب
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center bg-light">
                    <p class="mb-0">
                        لديك حساب بالفعل؟ 
                        <a href="{{ route('login') }}" class="text-decoration-none fw-bold">
                            تسجيل الدخول
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
