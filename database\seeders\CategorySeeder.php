<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Electronics',
                'name_ar' => 'الإلكترونيات',
                'description' => 'Electronic devices and accessories',
                'description_ar' => 'الأجهزة الإلكترونية والإكسسوارات',
                'icon' => 'fas fa-laptop',
                'children' => [
                    ['name' => 'Smartphones', 'name_ar' => 'الهواتف الذكية'],
                    ['name' => 'Laptops', 'name_ar' => 'أجهزة الكمبيوتر المحمولة'],
                    ['name' => 'Tablets', 'name_ar' => 'الأجهزة اللوحية'],
                    ['name' => 'Accessories', 'name_ar' => 'الإكسسوارات'],
                ]
            ],
            [
                'name' => 'Fashion & Clothing',
                'name_ar' => 'الأزياء والملابس',
                'description' => 'Clothing and fashion accessories',
                'description_ar' => 'الملابس وإكسسوارات الأزياء',
                'icon' => 'fas fa-tshirt',
                'children' => [
                    ['name' => 'Men\'s Clothing', 'name_ar' => 'ملابس رجالية'],
                    ['name' => 'Women\'s Clothing', 'name_ar' => 'ملابس نسائية'],
                    ['name' => 'Children\'s Clothing', 'name_ar' => 'ملابس أطفال'],
                    ['name' => 'Shoes', 'name_ar' => 'الأحذية'],
                ]
            ],
            [
                'name' => 'Home & Garden',
                'name_ar' => 'المنزل والحديقة',
                'description' => 'Home appliances and garden tools',
                'description_ar' => 'الأجهزة المنزلية وأدوات الحديقة',
                'icon' => 'fas fa-home',
                'children' => [
                    ['name' => 'Kitchen Appliances', 'name_ar' => 'أجهزة المطبخ'],
                    ['name' => 'Furniture', 'name_ar' => 'الأثاث'],
                    ['name' => 'Garden Tools', 'name_ar' => 'أدوات الحديقة'],
                    ['name' => 'Decoration', 'name_ar' => 'الديكور'],
                ]
            ],
            [
                'name' => 'Sports & Fitness',
                'name_ar' => 'الرياضة واللياقة',
                'description' => 'Sports equipment and fitness gear',
                'description_ar' => 'المعدات الرياضية وأدوات اللياقة',
                'icon' => 'fas fa-dumbbell',
                'children' => [
                    ['name' => 'Gym Equipment', 'name_ar' => 'معدات الجيم'],
                    ['name' => 'Outdoor Sports', 'name_ar' => 'الرياضات الخارجية'],
                    ['name' => 'Sportswear', 'name_ar' => 'الملابس الرياضية'],
                ]
            ],
            [
                'name' => 'Beauty & Personal Care',
                'name_ar' => 'الجمال والعناية الشخصية',
                'description' => 'Beauty products and personal care items',
                'description_ar' => 'منتجات التجميل والعناية الشخصية',
                'icon' => 'fas fa-spa',
                'children' => [
                    ['name' => 'Skincare', 'name_ar' => 'العناية بالبشرة'],
                    ['name' => 'Makeup', 'name_ar' => 'المكياج'],
                    ['name' => 'Hair Care', 'name_ar' => 'العناية بالشعر'],
                ]
            ],
            [
                'name' => 'Automotive',
                'name_ar' => 'السيارات',
                'description' => 'Car parts and automotive accessories',
                'description_ar' => 'قطع غيار السيارات والإكسسوارات',
                'icon' => 'fas fa-car',
                'children' => [
                    ['name' => 'Car Parts', 'name_ar' => 'قطع الغيار'],
                    ['name' => 'Car Accessories', 'name_ar' => 'إكسسوارات السيارات'],
                    ['name' => 'Tools', 'name_ar' => 'الأدوات'],
                ]
            ]
        ];

        foreach ($categories as $categoryData) {
            $category = Category::create([
                'name' => $categoryData['name'],
                'name_ar' => $categoryData['name_ar'],
                'description' => $categoryData['description'],
                'description_ar' => $categoryData['description_ar'],
                'icon' => $categoryData['icon'],
                'slug' => Str::slug($categoryData['name']),
                'is_active' => true,
                'sort_order' => 0,
            ]);

            // Create subcategories
            if (isset($categoryData['children'])) {
                foreach ($categoryData['children'] as $index => $childData) {
                    Category::create([
                        'name' => $childData['name'],
                        'name_ar' => $childData['name_ar'],
                        'parent_id' => $category->id,
                        'slug' => Str::slug($childData['name']),
                        'is_active' => true,
                        'sort_order' => $index,
                    ]);
                }
            }
        }
    }
}
