<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>لوحة التحكم - منصة البيع بالجملة</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        :root {
            --primary-color: #0A2463;
            --secondary-color: #FF6B35;
            --sidebar-width: 250px;
        }

        * {
            font-family: 'Cairo', sans-serif;
        }

        .bg-primary {
            background-color: var(--primary-color) !important;
        }

        .text-primary {
            color: var(--primary-color) !important;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .text-warning, .bg-warning {
            color: var(--secondary-color) !important;
            background-color: var(--secondary-color) !important;
        }

        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(180deg, var(--primary-color) 0%, #1e3a8a 100%);
            z-index: 1000;
            overflow-y: auto;
        }

        .main-content {
            margin-right: var(--sidebar-width);
            min-height: 100vh;
            background-color: #f8f9fa;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1.5rem;
            border-radius: 0;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 3px solid var(--secondary-color);
        }

        .sidebar .nav-link i {
            width: 20px;
            margin-left: 10px;
        }

        .stats-card {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #ff8c42 100%);
            color: white;
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-2px);
        }

        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .table th {
            background-color: var(--primary-color);
            color: white;
            border: none;
        }

        .badge-status {
            font-size: 0.75em;
            padding: 0.375rem 0.75rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }
        }

        .demo-badge {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 9999;
            background: var(--secondary-color);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- Demo Badge -->
    <div class="demo-badge">
        <i class="fas fa-rocket me-1"></i>
        نسخة تجريبية
    </div>

    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-3 text-center border-bottom border-secondary">
            <h5 class="text-white mb-0">
                <i class="fas fa-store me-2"></i>
                لوحة التحكم
            </h5>
            <small class="text-white-50">مدير النظام</small>
        </div>

        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                    <i class="fas fa-tachometer-alt"></i>
                    الرئيسية
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="#" onclick="showSection('products')">
                    <i class="fas fa-box"></i>
                    المنتجات
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="#" onclick="showSection('categories')">
                    <i class="fas fa-th-large"></i>
                    التصنيفات
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="#" onclick="showSection('orders')">
                    <i class="fas fa-shopping-bag"></i>
                    الطلبات
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="#" onclick="showSection('users')">
                    <i class="fas fa-users"></i>
                    المستخدمين
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="#" onclick="showSection('reports')">
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                </a>
            </li>

            <hr class="border-secondary">

            <li class="nav-item">
                <a class="nav-link" href="../demo.html" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    زيارة الموقع
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="#" onclick="showProfile()">
                    <i class="fas fa-user"></i>
                    الملف الشخصي
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="#" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container-fluid">
                <button class="btn btn-outline-primary d-md-none" type="button" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>

                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <span class="badge bg-danger">3</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">الإشعارات</h6></li>
                            <li><a class="dropdown-item" href="#">طلب جديد #1234</a></li>
                            <li><a class="dropdown-item" href="#">منتج نفد من المخزون</a></li>
                            <li><a class="dropdown-item" href="#">مراجعة جديدة</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <div class="container-fluid p-4">
            <!-- Dashboard Section -->
            <div id="dashboard-section">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="h3 mb-0">لوحة التحكم الرئيسية</h1>
                        <p class="text-muted">مرحباً مدير النظام، إليك نظرة عامة على أداء المنصة</p>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row g-4 mb-4">
                    <div class="col-xl-3 col-md-6">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <h3 class="fw-bold">1,250</h3>
                                <p class="mb-0">إجمالي المستخدمين</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-box fa-3x mb-3"></i>
                                <h3 class="fw-bold">5,000</h3>
                                <p class="mb-0">إجمالي المنتجات</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-shopping-bag fa-3x mb-3"></i>
                                <h3 class="fw-bold">15,000</h3>
                                <p class="mb-0">إجمالي الطلبات</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-dollar-sign fa-3x mb-3"></i>
                                <h3 class="fw-bold">2,500,000 ر.س</h3>
                                <p class="mb-0">إجمالي الإيرادات</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Stats -->
                <div class="row g-4 mb-4">
                    <div class="col-xl-3 col-md-6">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                <h4 class="text-warning">25</h4>
                                <p class="mb-0">طلبات معلقة</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-store fa-2x text-success mb-2"></i>
                                <h4 class="text-success">300</h4>
                                <p class="mb-0">موردين نشطين</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <i class="fas fa-user-friends fa-2x text-info mb-2"></i>
                                <h4 class="text-info">950</h4>
                                <p class="mb-0">تجار نشطين</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="card border-danger">
                            <div class="card-body text-center">
                                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                                <h4 class="text-danger">12</h4>
                                <p class="mb-0">منتجات قليلة المخزون</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row g-4">
                    <!-- Recent Orders -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-shopping-bag me-2"></i>
                                    أحدث الطلبات
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>رقم الطلب</th>
                                                <th>العميل</th>
                                                <th>المبلغ</th>
                                                <th>الحالة</th>
                                                <th>التاريخ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><a href="#" class="text-decoration-none">ORD001234</a></td>
                                                <td>أحمد محمد</td>
                                                <td>15,500 ر.س</td>
                                                <td><span class="badge bg-warning badge-status">معلق</span></td>
                                                <td>2024-01-15</td>
                                            </tr>
                                            <tr>
                                                <td><a href="#" class="text-decoration-none">ORD001233</a></td>
                                                <td>فاطمة علي</td>
                                                <td>8,750 ر.س</td>
                                                <td><span class="badge bg-info badge-status">قيد التجهيز</span></td>
                                                <td>2024-01-15</td>
                                            </tr>
                                            <tr>
                                                <td><a href="#" class="text-decoration-none">ORD001232</a></td>
                                                <td>خالد السعد</td>
                                                <td>22,300 ر.س</td>
                                                <td><span class="badge bg-primary badge-status">تم الشحن</span></td>
                                                <td>2024-01-14</td>
                                            </tr>
                                            <tr>
                                                <td><a href="#" class="text-decoration-none">ORD001231</a></td>
                                                <td>نورا أحمد</td>
                                                <td>12,900 ر.س</td>
                                                <td><span class="badge bg-success badge-status">تم التسليم</span></td>
                                                <td>2024-01-14</td>
                                            </tr>
                                            <tr>
                                                <td><a href="#" class="text-decoration-none">ORD001230</a></td>
                                                <td>محمد العتيبي</td>
                                                <td>18,650 ر.س</td>
                                                <td><span class="badge bg-success badge-status">تم التسليم</span></td>
                                                <td>2024-01-13</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Top Products -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header bg-warning text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-star me-2"></i>
                                    أكثر المنتجات مبيعاً
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="flex-shrink-0">
                                        <img src="https://via.placeholder.com/50x50/0A2463/FFFFFF?text=L"
                                             alt="لابتوب" class="rounded" width="50" height="50">
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1">لابتوب Dell XPS 13</h6>
                                        <small class="text-muted">مبيع: 150 قطعة</small>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <span class="badge bg-primary">3,800 ر.س</span>
                                    </div>
                                </div>

                                <div class="d-flex align-items-center mb-3">
                                    <div class="flex-shrink-0">
                                        <img src="https://via.placeholder.com/50x50/FF6B35/FFFFFF?text=P"
                                             alt="هاتف" class="rounded" width="50" height="50">
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1">هاتف Samsung Galaxy S23</h6>
                                        <small class="text-muted">مبيع: 120 قطعة</small>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <span class="badge bg-primary">2,700 ر.س</span>
                                    </div>
                                </div>

                                <div class="d-flex align-items-center mb-3">
                                    <div class="flex-shrink-0">
                                        <img src="https://via.placeholder.com/50x50/0A2463/FFFFFF?text=W"
                                             alt="ساعة" class="rounded" width="50" height="50">
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1">ساعة Apple Watch Series 9</h6>
                                        <small class="text-muted">مبيع: 95 قطعة</small>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <span class="badge bg-primary">1,500 ر.س</span>
                                    </div>
                                </div>

                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <img src="https://via.placeholder.com/50x50/FF6B35/FFFFFF?text=H"
                                             alt="سماعات" class="rounded" width="50" height="50">
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1">سماعات Sony WH-1000XM5</h6>
                                        <small class="text-muted">مبيع: 80 قطعة</small>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <span class="badge bg-primary">950 ر.س</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="row g-4 mt-4">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    المبيعات الشهرية
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="salesChart" height="100"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-user-friends me-2"></i>
                                    أحدث المستخدمين
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="flex-shrink-0">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1">سارة أحمد</h6>
                                        <small class="text-muted"><EMAIL></small>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <small class="text-muted">منذ ساعة</small>
                                    </div>
                                </div>

                                <div class="d-flex align-items-center mb-3">
                                    <div class="flex-shrink-0">
                                        <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center"
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1">عبدالله محمد</h6>
                                        <small class="text-muted"><EMAIL></small>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <small class="text-muted">منذ 3 ساعات</small>
                                    </div>
                                </div>

                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center"
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1">ليلى عبدالرحمن</h6>
                                        <small class="text-muted"><EMAIL></small>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <small class="text-muted">أمس</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other Sections (Hidden by default) -->
            <div id="products-section" style="display: none;">
                <div class="row mb-4">
                    <div class="col-12 d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0">إدارة المنتجات</h1>
                            <p class="text-muted">إدارة وتحرير المنتجات في المنصة</p>
                        </div>
                        <button class="btn btn-primary" onclick="showAddProduct()">
                            <i class="fas fa-plus me-2"></i>
                            إضافة منتج جديد
                        </button>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الصورة</th>
                                        <th>اسم المنتج</th>
                                        <th>التصنيف</th>
                                        <th>السعر</th>
                                        <th>المخزون</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><img src="https://via.placeholder.com/50x50" class="rounded" width="50"></td>
                                        <td>لابتوب Dell XPS 13</td>
                                        <td>الإلكترونيات</td>
                                        <td>3,800 ر.س</td>
                                        <td>25</td>
                                        <td><span class="badge bg-success">نشط</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary me-1" onclick="editProduct()">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteProduct()">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><img src="https://via.placeholder.com/50x50" class="rounded" width="50"></td>
                                        <td>هاتف Samsung Galaxy S23</td>
                                        <td>الإلكترونيات</td>
                                        <td>2,700 ر.س</td>
                                        <td>15</td>
                                        <td><span class="badge bg-success">نشط</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary me-1" onclick="editProduct()">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteProduct()">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div id="orders-section" style="display: none;">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="h3 mb-0">إدارة الطلبات</h1>
                        <p class="text-muted">متابعة وإدارة جميع الطلبات</p>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>العميل</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>حالة الطلب</th>
                                        <th>حالة الدفع</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>ORD001234</td>
                                        <td>أحمد محمد</td>
                                        <td>15,500 ر.س</td>
                                        <td><span class="badge bg-warning">معلق</span></td>
                                        <td><span class="badge bg-danger">غير مدفوع</span></td>
                                        <td>2024-01-15</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewOrder()">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div id="users-section" style="display: none;">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="h3 mb-0">إدارة المستخدمين</h1>
                        <p class="text-muted">إدارة حسابات المستخدمين والموردين</p>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>نوع الحساب</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>أحمد محمد</td>
                                        <td><EMAIL></td>
                                        <td><span class="badge bg-info">مورد</span></td>
                                        <td>2024-01-10</td>
                                        <td><span class="badge bg-success">نشط</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" onclick="editUser()">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div id="reports-section" style="display: none;">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="h3 mb-0">التقارير والإحصائيات</h1>
                        <p class="text-muted">تقارير مفصلة عن أداء المنصة</p>
                    </div>
                </div>

                <div class="row g-4">
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">تقرير المبيعات اليومية</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="dailySalesChart" height="200"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">توزيع المنتجات حسب التصنيف</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="categoryChart" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Navigation functionality
        function showSection(sectionName) {
            // Hide all sections
            const sections = ['dashboard', 'products', 'orders', 'users', 'reports'];
            sections.forEach(section => {
                const element = document.getElementById(section + '-section');
                if (element) {
                    element.style.display = 'none';
                }
            });

            // Show selected section
            const targetSection = document.getElementById(sectionName + '-section');
            if (targetSection) {
                targetSection.style.display = 'block';
            }

            // Update active nav link
            document.querySelectorAll('.sidebar .nav-link').forEach(link => {
                link.classList.remove('active');
            });
            event.target.classList.add('active');

            // Initialize charts if reports section
            if (sectionName === 'reports') {
                setTimeout(() => {
                    initReportsCharts();
                }, 100);
            }
        }

        function toggleSidebar() {
            document.querySelector('.sidebar').classList.toggle('show');
        }

        // Demo functions
        function showAddProduct() {
            alert('صفحة إضافة منتج جديد\n\nستتضمن:\n• تفاصيل المنتج\n• رفع الصور\n• تحديد الأسعار\n• إدارة المخزون');
        }

        function editProduct() {
            alert('صفحة تحرير المنتج\n\nيمكن تعديل جميع تفاصيل المنتج');
        }

        function deleteProduct() {
            if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
                alert('تم حذف المنتج بنجاح');
            }
        }

        function viewOrder() {
            alert('تفاصيل الطلب\n\nرقم الطلب: ORD001234\nالعميل: أحمد محمد\nالمبلغ: 15,500 ر.س\nالحالة: معلق');
        }

        function editUser() {
            alert('تحرير بيانات المستخدم\n\nيمكن تعديل:\n• المعلومات الشخصية\n• الصلاحيات\n• حالة الحساب');
        }

        function showProfile() {
            alert('الملف الشخصي\n\nاسم المستخدم: مدير النظام\nالبريد الإلكتروني: <EMAIL>\nآخر دخول: اليوم');
        }

        function logout() {
            if (confirm('هل تريد تسجيل الخروج؟')) {
                alert('تم تسجيل الخروج بنجاح');
                window.location.href = '../demo.html';
            }
        }

        // Charts initialization
        function initSalesChart() {
            const ctx = document.getElementById('salesChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                    datasets: [{
                        label: 'المبيعات (ر.س)',
                        data: [120000, 150000, 180000, 200000, 170000, 220000, 250000, 280000, 260000, 300000, 320000, 350000],
                        borderColor: '#FF6B35',
                        backgroundColor: 'rgba(255, 107, 53, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString() + ' ر.س';
                                }
                            }
                        }
                    }
                }
            });
        }

        function initReportsCharts() {
            // Daily Sales Chart
            const dailyCtx = document.getElementById('dailySalesChart');
            if (dailyCtx) {
                new Chart(dailyCtx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                        datasets: [{
                            label: 'المبيعات اليومية',
                            data: [12000, 15000, 18000, 14000, 16000, 13000, 11000],
                            backgroundColor: '#0A2463',
                            borderColor: '#0A2463',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }

            // Category Chart
            const categoryCtx = document.getElementById('categoryChart');
            if (categoryCtx) {
                new Chart(categoryCtx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: ['الإلكترونيات', 'الملابس', 'المنزل', 'الرياضة', 'الجمال', 'السيارات'],
                        datasets: [{
                            data: [1250, 890, 650, 420, 380, 290],
                            backgroundColor: [
                                '#0A2463',
                                '#FF6B35',
                                '#28a745',
                                '#ffc107',
                                '#dc3545',
                                '#6f42c1'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initSalesChart();

            // Welcome message
            setTimeout(() => {
                const welcome = document.createElement('div');
                welcome.className = 'position-fixed bottom-0 end-0 m-3 alert alert-success alert-dismissible fade show';
                welcome.style.zIndex = '9999';
                welcome.style.maxWidth = '300px';
                welcome.innerHTML = `
                    <h6><i class="fas fa-user-shield me-2"></i>مرحباً مدير النظام!</h6>
                    <p class="mb-2">هذه نسخة تجريبية من لوحة تحكم الإدارة</p>
                    <small>جميع الوظائف تفاعلية ومصممة لتوضيح إمكانيات النظام</small>
                    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
                `;
                document.body.appendChild(welcome);

                setTimeout(() => {
                    if (welcome.parentElement) {
                        welcome.remove();
                    }
                }, 8000);
            }, 1500);
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            const sidebar = document.querySelector('.sidebar');
            const toggleBtn = document.querySelector('[onclick="toggleSidebar()"]');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(e.target) &&
                !toggleBtn.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        });
    </script>
</body>
</html>