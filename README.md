# منصة البيع بالجملة B2B Wholesale Marketplace

منصة متخصصة في البيع بالجملة بين الشركات (B2B) تربط بين المصنعين/الموردين وتجار التجزئة، مع واجهة سهلة الاستخدام ولوحة تحكم متكاملة.

## الميزات الرئيسية

### 🎨 الواجهة الأمامية (Frontend)
- تصميم سريع الاستجابة (Responsive) يعمل على جميع الأجهزة
- وضع ليلي/نهاري قابل للتبديل مع حفظ التفضيلات
- تصنيفات منتجات منظمة مع فلترة متقدمة
- عروض وتخفيضات واضحة على المنتجات
- بحث ذكي بدعم الاقتراحات التلقائية
- صفحات تفاصيل المنتج شاملة (صور، فيديو، مواصفات)
- نظام تقييم ومراجعات للتجار

### 🛠️ لوحة التحكم (Admin Dashboard)
#### إدارة المنتجات:
- إضافة/تعديل/حذف المنتجات
- إدارة الفئات والعلامات التجارية
- تخصيص مواصفات المنتج (ألوان، مقاسات)
- إدارة الصور والفيديوهات المتعددة
- تحديد أسعار الجملة حسب الكميات
- إدارة التخفيضات والعروض
- تتبع المخزون

#### إدارة الطلبات:
- تتبع حالة الطلبات (جديد، قيد التجهيز، تم الشحن)
- إنشاء فواتير قابلة للطباعة
- إدارة المرتجعات

#### إدارة العملاء:
- تصنيف العملاء حسب حجم المشتريات
- نظام نقاط وخصومات ولاء
- إرسال إشعارات مخصصة

#### تقارير وتحليلات:
- إحصائيات المبيعات
- المنتجات الأكثر مبيعاً
- تحليل العملاء

## التقنيات المستخدمة

### 🎨 واجهة المستخدم:
- **HTML5, CSS3** مع **Bootstrap 5**
- **JavaScript (ES6+)**
- **Swiper.js** للعروض التقديمية
- **Chart.js** للرسوم البيانية

### ⚙️ الخلفية:
- **PHP** مع **Laravel Framework**
- **MySQL** لقاعدة البيانات
- **نظام API** للاتصال بالأنظمة الخارجية

### 🎨 التصميم:
- **خط Cairo** للغة العربية
- **ألوان أساسية**: أزرق داكن (#0A2463)، برتقالي (#FF6B35)، أبيض (#FFF)
- مساحات مريحة بين العناصر
- أيقونات **Font Awesome**

## متطلبات التشغيل

- PHP >= 8.1
- Composer
- MySQL >= 5.7
- Node.js & NPM (للتطوير)

## التثبيت

1. **استنساخ المشروع:**
```bash
git clone https://github.com/your-username/wholesale-marketplace.git
cd wholesale-marketplace
```

2. **تثبيت التبعيات:**
```bash
composer install
npm install
```

3. **إعداد البيئة:**
```bash
cp .env.example .env
php artisan key:generate
```

4. **إعداد قاعدة البيانات:**
- قم بإنشاء قاعدة بيانات MySQL جديدة
- حدث ملف `.env` بمعلومات قاعدة البيانات:
```env
DB_DATABASE=wholesale_marketplace
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

5. **تشغيل الهجرات:**
```bash
php artisan migrate
php artisan db:seed
```

6. **تشغيل الخادم:**
```bash
php artisan serve
```

## الاستخدام

### للمطورين:
```bash
# تشغيل الخادم المحلي
php artisan serve

# مراقبة تغييرات الملفات
npm run dev

# بناء الملفات للإنتاج
npm run build
```

### للمستخدمين:
1. قم بزيارة الموقع على `http://localhost:8000`
2. أنشئ حساب جديد أو سجل الدخول
3. تصفح المنتجات وأضفها للسلة
4. أكمل عملية الشراء

## الأدوار والصلاحيات

### 👤 المشتري (Retailer):
- تصفح المنتجات
- إضافة المنتجات للسلة
- إنشاء الطلبات
- تتبع الطلبات
- كتابة المراجعات

### 🏭 المورد (Supplier):
- إدارة المنتجات الخاصة
- تتبع الطلبات
- إدارة المخزون
- عرض التقارير

### 👨‍💼 المدير (Admin):
- إدارة جميع المنتجات
- إدارة المستخدمين
- إدارة التصنيفات
- عرض التقارير الشاملة
- إدارة إعدادات النظام

## المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. أنشئ فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. افتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم، يرجى:
- فتح issue في GitHub
- إرسال بريد إلكتروني إلى: <EMAIL>
- زيارة مركز المساعدة: [help.wholesale-marketplace.com](https://help.wholesale-marketplace.com)

## الشكر والتقدير

- [Laravel](https://laravel.com) - إطار العمل الرائع
- [Bootstrap](https://getbootstrap.com) - مكتبة CSS
- [Font Awesome](https://fontawesome.com) - الأيقونات
- [Swiper.js](https://swiperjs.com) - العروض التقديمية
- [Chart.js](https://www.chartjs.org) - الرسوم البيانية

---

صُنع بـ ❤️ في المملكة العربية السعودية
