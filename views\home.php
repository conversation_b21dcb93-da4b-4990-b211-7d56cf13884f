<?php
$title = 'الرئيسية - منصة البيع بالجملة';
ob_start();
?>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold text-white mb-4">
                    منصة البيع بالجملة الرائدة
                </h1>
                <p class="lead text-white mb-4">
                    اكتشف أفضل المنتجات بأسعار الجملة المناسبة لتجارتك. 
                    نربط بين المصنعين والموردين وتجار التجزئة في مكان واحد.
                </p>
                <div class="d-flex gap-3">
                    <a href="/products" class="btn btn-warning btn-lg">
                        <i class="fas fa-shopping-bag me-2"></i>
                        تسوق الآن
                    </a>
                    <?php if (!isLoggedIn()): ?>
                    <a href="/login" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-user-plus me-2"></i>
                        تسجيل الدخول
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <img src="https://via.placeholder.com/500x400/FF6B35/FFFFFF?text=B2B+Marketplace" 
                     alt="B2B Marketplace" class="img-fluid rounded shadow">
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="card stats-card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <h3 class="fw-bold"><?= number_format(count(DB::$users)) ?>+</h3>
                        <p class="mb-0">مستخدم نشط</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card stats-card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-box fa-3x mb-3"></i>
                        <h3 class="fw-bold"><?= number_format(count(DB::$products)) ?>+</h3>
                        <p class="mb-0">منتج متنوع</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card stats-card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-truck fa-3x mb-3"></i>
                        <h3 class="fw-bold"><?= number_format(count(DB::$orders)) ?>+</h3>
                        <p class="mb-0">طلب مكتمل</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card stats-card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-store fa-3x mb-3"></i>
                        <h3 class="fw-bold">100+</h3>
                        <p class="mb-0">مورد موثوق</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Products -->
<section class="py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="fw-bold mb-3">المنتجات المميزة</h2>
                <p class="text-muted">أفضل المنتجات المختارة خصيصاً لك</p>
            </div>
        </div>
        
        <div class="row g-4">
            <?php foreach (DB::$products as $product): ?>
            <div class="col-lg-3 col-md-6">
                <div class="card product-card h-100">
                    <div class="position-relative">
                        <img src="https://via.placeholder.com/300x200/0A2463/FFFFFF?text=<?= urlencode(substr($product['name'], 0, 10)) ?>" 
                             class="card-img-top" alt="<?= htmlspecialchars($product['name']) ?>" style="height: 200px; object-fit: cover;">
                        <?php if ($product['price'] > $product['wholesale_price']): ?>
                        <span class="position-absolute top-0 end-0 m-2 badge bg-danger">
                            -<?= round((($product['price'] - $product['wholesale_price']) / $product['price']) * 100) ?>%
                        </span>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <h6 class="card-title fw-bold"><?= htmlspecialchars($product['name']) ?></h6>
                        <div class="mb-2">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <small class="text-muted ms-1">(4.8)</small>
                        </div>
                        <div class="price-section">
                            <?php if ($product['price'] > $product['wholesale_price']): ?>
                            <span class="text-decoration-line-through text-muted"><?= number_format($product['price']) ?> ر.س</span><br>
                            <?php endif; ?>
                            <span class="text-warning fw-bold fs-5"><?= number_format($product['wholesale_price']) ?> ر.س</span>
                            <small class="text-muted d-block">سعر الجملة</small>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-box me-1"></i>
                                متوفر: <?= $product['stock'] ?> قطعة
                            </small>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <button class="btn btn-primary w-100" onclick="addToCart(<?= $product['id'] ?>)">
                            <i class="fas fa-cart-plus me-2"></i>
                            أضف للسلة
                        </button>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="/products" class="btn btn-outline-primary btn-lg">
                عرض جميع المنتجات
                <i class="fas fa-arrow-left ms-2"></i>
            </a>
        </div>
    </div>
</section>

<!-- Why Choose Us -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="fw-bold mb-3">لماذا تختارنا؟</h2>
                <p class="text-muted">نقدم أفضل الخدمات لضمان نجاح تجارتك</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="text-center">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-shield-alt fa-2x"></i>
                    </div>
                    <h5 class="fw-bold">ضمان الجودة</h5>
                    <p class="text-muted">جميع منتجاتنا مضمونة الجودة ومن موردين موثوقين</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="text-center">
                    <div class="bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-shipping-fast fa-2x"></i>
                    </div>
                    <h5 class="fw-bold">شحن سريع</h5>
                    <p class="text-muted">توصيل سريع وآمن لجميع أنحاء المملكة</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="text-center">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-headset fa-2x"></i>
                    </div>
                    <h5 class="fw-bold">دعم 24/7</h5>
                    <p class="text-muted">فريق دعم متاح على مدار الساعة لمساعدتك</p>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
function addToCart(productId) {
    <?php if (isLoggedIn()): ?>
    alert('تم إضافة المنتج إلى السلة بنجاح! 🛒');
    <?php else: ?>
    if (confirm('يجب تسجيل الدخول أولاً. هل تريد الانتقال لصفحة تسجيل الدخول؟')) {
        window.location.href = '/login';
    }
    <?php endif; ?>
}
</script>

<?php
$content = ob_get_clean();
include 'layout.php';
?>
