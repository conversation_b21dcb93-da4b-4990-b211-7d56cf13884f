@extends('layouts.app')

@section('title', 'الرئيسية')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold text-white mb-4">
                    منصة البيع بالجملة الرائدة
                </h1>
                <p class="lead text-white mb-4">
                    اكتشف أفضل المنتجات بأسعار الجملة المناسبة لتجارتك. 
                    نربط بين المصنعين والموردين وتجار التجزئة في مكان واحد.
                </p>
                <div class="d-flex gap-3">
                    <a href="{{ route('products.index') }}" class="btn btn-warning btn-lg">
                        <i class="fas fa-shopping-bag me-2"></i>
                        تسوق الآن
                    </a>
                    <a href="{{ route('register') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-user-plus me-2"></i>
                        انضم كمورد
                    </a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <img src="https://via.placeholder.com/500x400/FF6B35/FFFFFF?text=B2B+Marketplace" 
                     alt="B2B Marketplace" class="img-fluid rounded shadow">
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="card stats-card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <h3 class="fw-bold">{{ number_format(1250) }}+</h3>
                        <p class="mb-0">عميل راضٍ</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card stats-card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-box fa-3x mb-3"></i>
                        <h3 class="fw-bold">{{ number_format(5000) }}+</h3>
                        <p class="mb-0">منتج متنوع</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card stats-card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-truck fa-3x mb-3"></i>
                        <h3 class="fw-bold">{{ number_format(15000) }}+</h3>
                        <p class="mb-0">طلب مكتمل</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card stats-card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-store fa-3x mb-3"></i>
                        <h3 class="fw-bold">{{ number_format(300) }}+</h3>
                        <p class="mb-0">مورد موثوق</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="fw-bold mb-3">تصنيفات المنتجات</h2>
                <p class="text-muted">اختر من بين مجموعة واسعة من التصنيفات</p>
            </div>
        </div>
        <div class="row g-4">
            @php
            $categories = [
                ['name' => 'الإلكترونيات', 'icon' => 'fas fa-laptop', 'count' => 1250],
                ['name' => 'الملابس والأزياء', 'icon' => 'fas fa-tshirt', 'count' => 890],
                ['name' => 'المنزل والحديقة', 'icon' => 'fas fa-home', 'count' => 650],
                ['name' => 'الرياضة واللياقة', 'icon' => 'fas fa-dumbbell', 'count' => 420],
                ['name' => 'الجمال والعناية', 'icon' => 'fas fa-spa', 'count' => 380],
                ['name' => 'السيارات', 'icon' => 'fas fa-car', 'count' => 290],
            ];
            @endphp
            
            @foreach($categories as $category)
            <div class="col-lg-2 col-md-4 col-6">
                <a href="#" class="category-card card text-center h-100">
                    <div class="card-body">
                        <i class="{{ $category['icon'] }} fa-3x text-primary mb-3"></i>
                        <h6 class="fw-bold">{{ $category['name'] }}</h6>
                        <small class="text-muted">{{ number_format($category['count']) }} منتج</small>
                    </div>
                </a>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Featured Products -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="fw-bold mb-3">المنتجات المميزة</h2>
                <p class="text-muted">أفضل المنتجات المختارة خصيصاً لك</p>
            </div>
        </div>
        
        <div class="row g-4">
            @php
            $featuredProducts = [
                [
                    'name' => 'لابتوب Dell XPS 13',
                    'price' => 4500,
                    'wholesale_price' => 3800,
                    'discount' => 15,
                    'rating' => 4.8,
                    'image' => 'https://via.placeholder.com/300x200/0A2463/FFFFFF?text=Laptop'
                ],
                [
                    'name' => 'هاتف Samsung Galaxy S23',
                    'price' => 3200,
                    'wholesale_price' => 2700,
                    'discount' => 20,
                    'rating' => 4.9,
                    'image' => 'https://via.placeholder.com/300x200/FF6B35/FFFFFF?text=Phone'
                ],
                [
                    'name' => 'ساعة Apple Watch Series 9',
                    'price' => 1800,
                    'wholesale_price' => 1500,
                    'discount' => 10,
                    'rating' => 4.7,
                    'image' => 'https://via.placeholder.com/300x200/0A2463/FFFFFF?text=Watch'
                ],
                [
                    'name' => 'سماعات Sony WH-1000XM5',
                    'price' => 1200,
                    'wholesale_price' => 950,
                    'discount' => 25,
                    'rating' => 4.6,
                    'image' => 'https://via.placeholder.com/300x200/FF6B35/FFFFFF?text=Headphones'
                ]
            ];
            @endphp
            
            @foreach($featuredProducts as $product)
            <div class="col-lg-3 col-md-6">
                <div class="card product-card h-100">
                    <div class="position-relative">
                        <img src="{{ $product['image'] }}" class="card-img-top product-image" alt="{{ $product['name'] }}">
                        @if($product['discount'] > 0)
                        <span class="discount-badge">-{{ $product['discount'] }}%</span>
                        @endif
                    </div>
                    <div class="card-body">
                        <h6 class="card-title fw-bold">{{ $product['name'] }}</h6>
                        <div class="rating-stars mb-2">
                            @for($i = 1; $i <= 5; $i++)
                                @if($i <= floor($product['rating']))
                                    <i class="fas fa-star"></i>
                                @elseif($i <= $product['rating'])
                                    <i class="fas fa-star-half-alt"></i>
                                @else
                                    <i class="far fa-star"></i>
                                @endif
                            @endfor
                            <small class="text-muted ms-1">({{ $product['rating'] }})</small>
                        </div>
                        <div class="price-section">
                            @if($product['discount'] > 0)
                            <span class="price-original">{{ number_format($product['price']) }} ر.س</span><br>
                            @endif
                            <span class="price-discounted">{{ number_format($product['wholesale_price']) }} ر.س</span>
                            <small class="text-muted d-block">سعر الجملة</small>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <button class="btn btn-primary w-100" onclick="addToCart({{ $loop->index + 1 }})">
                            <i class="fas fa-cart-plus me-2"></i>
                            أضف للسلة
                        </button>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        <div class="text-center mt-4">
            <a href="{{ route('products.index') }}" class="btn btn-outline-primary btn-lg">
                عرض جميع المنتجات
                <i class="fas fa-arrow-left ms-2"></i>
            </a>
        </div>
    </div>
</section>

<!-- Why Choose Us -->
<section class="py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="fw-bold mb-3">لماذا تختارنا؟</h2>
                <p class="text-muted">نقدم أفضل الخدمات لضمان نجاح تجارتك</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="text-center">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-shield-alt fa-2x"></i>
                    </div>
                    <h5 class="fw-bold">ضمان الجودة</h5>
                    <p class="text-muted">جميع منتجاتنا مضمونة الجودة ومن موردين موثوقين</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="text-center">
                    <div class="bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-shipping-fast fa-2x"></i>
                    </div>
                    <h5 class="fw-bold">شحن سريع</h5>
                    <p class="text-muted">توصيل سريع وآمن لجميع أنحاء المملكة</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="text-center">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-headset fa-2x"></i>
                    </div>
                    <h5 class="fw-bold">دعم 24/7</h5>
                    <p class="text-muted">فريق دعم متاح على مدار الساعة لمساعدتك</p>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
