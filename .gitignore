# Laravel
/vendor/
/node_modules/
/public/hot
/public/storage
/storage/*.key
/storage/app/public/*
!/storage/app/public/.gitignore

# Environment files
.env
.env.backup
.env.production

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Laravel Mix
/public/mix-manifest.json

# Laravel Nova
/nova

# Homestead
Homestead.json
Homestead.yaml

# PHP CS Fixer
.php_cs.cache

# PHPUnit
.phpunit.result.cache

# Psalm
psalm.xml

# Local development
/public/build/
/public/css/
/public/js/
