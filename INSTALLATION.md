# دليل التثبيت والتشغيل - منصة البيع بالجملة

## متطلبات النظام

- **PHP** >= 8.1
- **Composer** (لإدارة تبعيات PHP)
- **Node.js** >= 16.x و **NPM** (لإدارة تبعيات JavaScript)
- **MySQL** >= 5.7 أو **MariaDB** >= 10.3
- **Git** (لاستنساخ المشروع)

## خطوات التثبيت

### 1. استنساخ المشروع
```bash
git clone https://github.com/your-username/wholesale-marketplace.git
cd wholesale-marketplace
```

### 2. تثبيت تبعيات PHP
```bash
composer install
```

### 3. تثبيت تبعيات JavaScript
```bash
npm install
```

### 4. إعداد ملف البيئة
```bash
# نسخ ملف البيئة
cp .env.example .env

# توليد مفتاح التطبيق
php artisan key:generate
```

### 5. إعداد قاعدة البيانات

#### إنشاء قاعدة البيانات:
```sql
CREATE DATABASE wholesale_marketplace CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### تحديث ملف .env:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=wholesale_marketplace
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 6. تشغيل الهجرات والبذور
```bash
# تشغيل الهجرات
php artisan migrate

# تشغيل البذور (بيانات تجريبية)
php artisan db:seed
```

### 7. إنشاء رابط التخزين
```bash
php artisan storage:link
```

### 8. بناء الأصول الأمامية
```bash
# للتطوير
npm run dev

# للإنتاج
npm run build
```

### 9. تشغيل الخادم
```bash
php artisan serve
```

الآن يمكنك زيارة الموقع على: `http://localhost:8000`

## حسابات المستخدمين التجريبية

### مدير النظام:
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** password

### مورد:
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** password

### تاجر تجزئة:
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** password

## إعدادات إضافية

### إعداد البريد الإلكتروني (اختياري)
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Wholesale Marketplace"
```

### إعداد التخزين السحابي (اختياري)
```env
FILESYSTEM_DISK=s3
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your-bucket-name
```

## أوامر مفيدة

### تحديث التبعيات:
```bash
composer update
npm update
```

### مسح التخزين المؤقت:
```bash
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

### إعادة تحميل الفئات والصلاحيات:
```bash
php artisan db:seed --class=RoleSeeder
```

### تشغيل الاختبارات:
```bash
php artisan test
```

## استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات:
1. تأكد من تشغيل خادم MySQL
2. تحقق من صحة بيانات الاتصال في ملف `.env`
3. تأكد من وجود قاعدة البيانات

### خطأ في الصلاحيات:
```bash
# إعطاء صلاحيات للمجلدات
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
```

### خطأ في مفتاح التطبيق:
```bash
php artisan key:generate
```

### مشاكل في الأصول الأمامية:
```bash
# حذف node_modules وإعادة التثبيت
rm -rf node_modules/
npm install
npm run dev
```

## الدعم

للحصول على المساعدة:
- راجع ملف [README.md](README.md)
- افتح issue في GitHub
- راسلنا على: <EMAIL>

---

**ملاحظة:** هذا المشروع في مرحلة التطوير. يرجى عدم استخدامه في بيئة الإنتاج بدون اختبار شامل.
