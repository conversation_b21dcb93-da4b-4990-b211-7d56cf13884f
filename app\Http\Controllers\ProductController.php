<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    /**
     * Display a listing of products.
     */
    public function index(Request $request)
    {
        $query = Product::with(['category', 'supplier', 'reviews'])
            ->active();

        // Filter by category
        if ($request->filled('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        // Filter by price range
        if ($request->filled('min_price')) {
            $query->where('wholesale_price', '>=', $request->min_price);
        }
        if ($request->filled('max_price')) {
            $query->where('wholesale_price', '<=', $request->max_price);
        }

        // Filter by brand
        if ($request->filled('brand')) {
            $query->where('brand', $request->brand);
        }

        // Search by name
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('name_ar', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhere('sku', 'like', "%{$searchTerm}%");
            });
        }

        // Sort
        $sortBy = $request->get('sort', 'latest');
        switch ($sortBy) {
            case 'price_low':
                $query->orderBy('wholesale_price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('wholesale_price', 'desc');
                break;
            case 'name':
                $query->orderBy('name', 'asc');
                break;
            case 'rating':
                $query->withAvg('reviews', 'rating')->orderBy('reviews_avg_rating', 'desc');
                break;
            default:
                $query->latest();
        }

        $products = $query->paginate(12)->withQueryString();

        // Get filter options
        $categories = Category::active()->parent()->orderBy('name')->get();
        $brands = Product::active()->distinct()->pluck('brand')->filter()->sort();
        $priceRange = [
            'min' => Product::active()->min('wholesale_price'),
            'max' => Product::active()->max('wholesale_price')
        ];

        return view('products.index', compact(
            'products',
            'categories',
            'brands',
            'priceRange'
        ));
    }

    /**
     * Display the specified product.
     */
    public function show($slug)
    {
        $product = Product::with([
            'category',
            'supplier',
            'reviews.user',
            'reviews' => function ($query) {
                $query->approved()->latest();
            }
        ])->where('slug', $slug)->active()->firstOrFail();

        // Get related products
        $relatedProducts = Product::with(['category', 'supplier'])
            ->where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->active()
            ->take(4)
            ->get();

        // Calculate average rating
        $averageRating = $product->reviews->avg('rating') ?? 0;
        $totalReviews = $product->reviews->count();

        return view('products.show', compact(
            'product',
            'relatedProducts',
            'averageRating',
            'totalReviews'
        ));
    }

    /**
     * Display offers page.
     */
    public function offers(Request $request)
    {
        $query = Product::with(['category', 'supplier', 'reviews'])
            ->active()
            ->where('discount_percentage', '>', 0)
            ->where('discount_start_date', '<=', now())
            ->where('discount_end_date', '>=', now());

        // Apply filters similar to index method
        if ($request->filled('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        $products = $query->orderBy('discount_percentage', 'desc')->paginate(12);

        $categories = Category::active()->parent()->orderBy('name')->get();

        return view('products.offers', compact('products', 'categories'));
    }
}
