<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Product;
use App\Models\Order;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminDashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        // Get statistics
        $stats = [
            'total_users' => User::count(),
            'total_products' => Product::count(),
            'total_orders' => Order::count(),
            'total_revenue' => Order::where('payment_status', 'paid')->sum('final_amount'),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'active_suppliers' => User::role('supplier')->where('is_active', true)->count(),
            'active_retailers' => User::role('retailer')->where('is_active', true)->count(),
            'low_stock_products' => Product::where('stock_quantity', '<', 10)->count(),
        ];

        // Recent orders
        $recentOrders = Order::with(['user'])
            ->latest()
            ->take(5)
            ->get();

        // Top selling products
        $topProducts = Product::withCount(['orderItems as total_sold' => function ($query) {
                $query->select(DB::raw('SUM(quantity)'));
            }])
            ->orderBy('total_sold', 'desc')
            ->take(5)
            ->get();

        // Monthly sales data for chart
        $monthlySales = Order::where('payment_status', 'paid')
            ->where('created_at', '>=', now()->subMonths(12))
            ->selectRaw('MONTH(created_at) as month, SUM(final_amount) as total')
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Recent users
        $recentUsers = User::latest()
            ->take(5)
            ->get();

        return view('admin.dashboard', compact(
            'stats',
            'recentOrders',
            'topProducts',
            'monthlySales',
            'recentUsers'
        ));
    }

    /**
     * Display supplier dashboard.
     */
    public function supplierDashboard()
    {
        $supplier = auth()->user();
        
        $stats = [
            'total_products' => $supplier->products()->count(),
            'active_products' => $supplier->products()->active()->count(),
            'total_orders' => Order::whereHas('orderItems.product', function ($query) use ($supplier) {
                $query->where('supplier_id', $supplier->id);
            })->count(),
            'total_revenue' => Order::whereHas('orderItems.product', function ($query) use ($supplier) {
                $query->where('supplier_id', $supplier->id);
            })->where('payment_status', 'paid')->sum('final_amount'),
        ];

        $recentOrders = Order::whereHas('orderItems.product', function ($query) use ($supplier) {
                $query->where('supplier_id', $supplier->id);
            })
            ->with(['user', 'orderItems.product'])
            ->latest()
            ->take(5)
            ->get();

        $topProducts = $supplier->products()
            ->withCount(['orderItems as total_sold' => function ($query) {
                $query->select(DB::raw('SUM(quantity)'));
            }])
            ->orderBy('total_sold', 'desc')
            ->take(5)
            ->get();

        return view('admin.supplier-dashboard', compact(
            'stats',
            'recentOrders',
            'topProducts'
        ));
    }

    /**
     * Sales report.
     */
    public function salesReport(Request $request)
    {
        $startDate = $request->get('start_date', now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));

        $orders = Order::whereBetween('created_at', [$startDate, $endDate])
            ->with(['user', 'orderItems.product'])
            ->get();

        $totalRevenue = $orders->where('payment_status', 'paid')->sum('final_amount');
        $totalOrders = $orders->count();
        $averageOrderValue = $totalOrders > 0 ? $totalRevenue / $totalOrders : 0;

        return view('admin.reports.sales', compact(
            'orders',
            'totalRevenue',
            'totalOrders',
            'averageOrderValue',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Products report.
     */
    public function productsReport()
    {
        $products = Product::withCount(['orderItems as total_sold' => function ($query) {
                $query->select(DB::raw('SUM(quantity)'));
            }])
            ->with(['category', 'supplier'])
            ->orderBy('total_sold', 'desc')
            ->paginate(20);

        $lowStockProducts = Product::where('stock_quantity', '<', 10)
            ->with(['category', 'supplier'])
            ->get();

        return view('admin.reports.products', compact(
            'products',
            'lowStockProducts'
        ));
    }

    /**
     * Customers report.
     */
    public function customersReport()
    {
        $customers = User::role('retailer')
            ->withCount('orders')
            ->withSum('orders as total_spent', 'final_amount')
            ->orderBy('total_spent', 'desc')
            ->paginate(20);

        $topCustomers = User::role('retailer')
            ->withSum('orders as total_spent', 'final_amount')
            ->orderBy('total_spent', 'desc')
            ->take(10)
            ->get();

        return view('admin.reports.customers', compact(
            'customers',
            'topCustomers'
        ));
    }
}
