<?php
$title = 'لوحة التحكم الرئيسية';
ob_start();
?>

<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">لوحة التحكم الرئيسية</h1>
        <p class="text-muted">مرحباً <?= htmlspecialchars(getCurrentUser()['name']) ?>، إليك نظرة عامة على أداء المنصة</p>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row g-4 mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-users fa-3x mb-3"></i>
                <h3 class="fw-bold"><?= number_format(count(DB::$users)) ?></h3>
                <p class="mb-0">إجمالي المستخدمين</p>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-box fa-3x mb-3"></i>
                <h3 class="fw-bold"><?= number_format(count(DB::$products)) ?></h3>
                <p class="mb-0">إجمالي المنتجات</p>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-shopping-bag fa-3x mb-3"></i>
                <h3 class="fw-bold"><?= number_format(count(DB::$orders)) ?></h3>
                <p class="mb-0">إجمالي الطلبات</p>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-dollar-sign fa-3x mb-3"></i>
                <h3 class="fw-bold"><?= number_format(array_sum(array_column(DB::$orders, 'total'))) ?> ر.س</h3>
                <p class="mb-0">إجمالي الإيرادات</p>
            </div>
        </div>
    </div>
</div>

<!-- Additional Stats -->
<div class="row g-4 mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                <h4 class="text-warning"><?= count(array_filter(DB::$orders, fn($o) => $o['status'] === 'pending')) ?></h4>
                <p class="mb-0">طلبات معلقة</p>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-store fa-2x text-success mb-2"></i>
                <h4 class="text-success"><?= count(array_filter(DB::$users, fn($u) => $u['role'] === 'supplier')) ?></h4>
                <p class="mb-0">موردين نشطين</p>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-user-friends fa-2x text-info mb-2"></i>
                <h4 class="text-info"><?= count(array_filter(DB::$users, fn($u) => $u['role'] === 'retailer')) ?></h4>
                <p class="mb-0">تجار نشطين</p>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card border-danger">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                <h4 class="text-danger"><?= count(array_filter(DB::$products, fn($p) => $p['stock'] < 10)) ?></h4>
                <p class="mb-0">منتجات قليلة المخزون</p>
            </div>
        </div>
    </div>
</div>

<div class="row g-4">
    <!-- Recent Orders -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-bag me-2"></i>
                    أحدث الطلبات
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (DB::$orders as $order): ?>
                            <?php 
                            $user = array_filter(DB::$users, fn($u) => $u['id'] === $order['user_id'])[0] ?? null;
                            $statusColors = [
                                'pending' => 'warning',
                                'processing' => 'info',
                                'shipped' => 'primary',
                                'delivered' => 'success',
                                'cancelled' => 'danger'
                            ];
                            $statusNames = [
                                'pending' => 'معلق',
                                'processing' => 'قيد التجهيز',
                                'shipped' => 'تم الشحن',
                                'delivered' => 'تم التسليم',
                                'cancelled' => 'ملغي'
                            ];
                            ?>
                            <tr>
                                <td>
                                    <a href="/admin/orders?id=<?= $order['id'] ?>" class="text-decoration-none">
                                        <?= htmlspecialchars($order['order_number']) ?>
                                    </a>
                                </td>
                                <td><?= $user ? htmlspecialchars($user['name']) : 'غير معروف' ?></td>
                                <td><?= number_format($order['total']) ?> ر.س</td>
                                <td>
                                    <span class="badge bg-<?= $statusColors[$order['status']] ?? 'secondary' ?> badge-status">
                                        <?= $statusNames[$order['status']] ?? $order['status'] ?>
                                    </span>
                                </td>
                                <td><?= $order['date'] ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Products -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2"></i>
                    أكثر المنتجات مبيعاً
                </h5>
            </div>
            <div class="card-body">
                <?php foreach (array_slice(DB::$products, 0, 4) as $product): ?>
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <img src="https://via.placeholder.com/50x50/0A2463/FFFFFF?text=<?= substr($product['name'], 0, 1) ?>" 
                             alt="<?= htmlspecialchars($product['name']) ?>" 
                             class="rounded" 
                             width="50" 
                             height="50">
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1"><?= htmlspecialchars(substr($product['name'], 0, 30)) ?>...</h6>
                        <small class="text-muted">
                            مبيع: <?= rand(50, 200) ?> قطعة
                        </small>
                    </div>
                    <div class="flex-shrink-0">
                        <span class="badge bg-primary"><?= number_format($product['wholesale_price']) ?> ر.س</span>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row g-4 mt-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    المبيعات الشهرية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="salesChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-friends me-2"></i>
                    أحدث المستخدمين
                </h5>
            </div>
            <div class="card-body">
                <?php foreach (array_slice(DB::$users, 1) as $user): ?>
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                             style="width: 40px; height: 40px;">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1"><?= htmlspecialchars($user['name']) ?></h6>
                        <small class="text-muted"><?= htmlspecialchars($user['email']) ?></small>
                    </div>
                    <div class="flex-shrink-0">
                        <span class="badge bg-<?= $user['role'] === 'supplier' ? 'success' : 'info' ?>">
                            <?= $user['role'] === 'supplier' ? 'مورد' : 'تاجر' ?>
                        </span>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<script>
// Sales Chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('salesChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            datasets: [{
                label: 'المبيعات (ر.س)',
                data: [120000, 150000, 180000, 200000, 170000, 220000, 250000, 280000, 260000, 300000, 320000, 350000],
                borderColor: '#FF6B35',
                backgroundColor: 'rgba(255, 107, 53, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' ر.س';
                        }
                    }
                }
            }
        }
    });
});
</script>

<?php
$content = ob_get_clean();
include 'layout.php';
?>
