<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles
        $adminRole = Role::create(['name' => 'admin']);
        $supplierRole = Role::create(['name' => 'supplier']);
        $retailerRole = Role::create(['name' => 'retailer']);

        // Create permissions
        $permissions = [
            // Product permissions
            'view products',
            'create products',
            'edit products',
            'delete products',
            
            // Category permissions
            'view categories',
            'create categories',
            'edit categories',
            'delete categories',
            
            // Order permissions
            'view orders',
            'create orders',
            'edit orders',
            'delete orders',
            
            // User permissions
            'view users',
            'create users',
            'edit users',
            'delete users',
            
            // Admin permissions
            'access admin',
            'view reports',
            'manage settings',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Assign permissions to roles
        $adminRole->givePermissionTo(Permission::all());

        $supplierRole->givePermissionTo([
            'view products',
            'create products',
            'edit products',
            'view orders',
            'edit orders',
            'access admin',
        ]);

        $retailerRole->givePermissionTo([
            'view products',
            'create orders',
            'view orders',
        ]);
    }
}
