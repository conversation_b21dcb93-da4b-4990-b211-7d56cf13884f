<?php
$title = 'إدارة المنتجات';
ob_start();
?>

<div class="row mb-4">
    <div class="col-12 d-flex justify-content-between align-items-center">
        <div>
            <h1 class="h3 mb-0">إدارة المنتجات</h1>
            <p class="text-muted">إدارة وتحرير المنتجات في المنصة</p>
        </div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
            <i class="fas fa-plus me-2"></i>
            إضافة منتج جديد
        </button>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الصورة</th>
                        <th>اسم المنتج</th>
                        <th>التصنيف</th>
                        <th>السعر الأصلي</th>
                        <th>سعر الجملة</th>
                        <th>المخزون</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach (DB::$products as $product): ?>
                    <tr>
                        <td>
                            <img src="https://via.placeholder.com/50x50/0A2463/FFFFFF?text=<?= substr($product['name'], 0, 1) ?>" 
                                 class="rounded" width="50" height="50">
                        </td>
                        <td>
                            <strong><?= htmlspecialchars($product['name']) ?></strong>
                        </td>
                        <td><?= htmlspecialchars($product['category']) ?></td>
                        <td><?= number_format($product['price']) ?> ر.س</td>
                        <td>
                            <span class="text-warning fw-bold"><?= number_format($product['wholesale_price']) ?> ر.س</span>
                        </td>
                        <td>
                            <span class="badge bg-<?= $product['stock'] < 10 ? 'danger' : 'success' ?>">
                                <?= $product['stock'] ?> قطعة
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-success">نشط</span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-outline-primary" 
                                        onclick="editProduct(<?= $product['id'] ?>)"
                                        title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-info" 
                                        onclick="viewProduct(<?= $product['id'] ?>)"
                                        title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteProduct(<?= $product['id'] ?>)"
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    إضافة منتج جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addProductForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="productName" class="form-label">اسم المنتج</label>
                            <input type="text" class="form-control" id="productName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="productCategory" class="form-label">التصنيف</label>
                            <select class="form-select" id="productCategory" required>
                                <option value="">اختر التصنيف</option>
                                <option value="الإلكترونيات">الإلكترونيات</option>
                                <option value="الملابس">الملابس</option>
                                <option value="المنزل">المنزل والحديقة</option>
                                <option value="الرياضة">الرياضة واللياقة</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="productPrice" class="form-label">السعر الأصلي (ر.س)</label>
                            <input type="number" class="form-control" id="productPrice" step="0.01" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="wholesalePrice" class="form-label">سعر الجملة (ر.س)</label>
                            <input type="number" class="form-control" id="wholesalePrice" step="0.01" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="productStock" class="form-label">كمية المخزون</label>
                            <input type="number" class="form-control" id="productStock" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="productImage" class="form-label">صورة المنتج</label>
                            <input type="file" class="form-control" id="productImage" accept="image/*">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="productDescription" class="form-label">وصف المنتج</label>
                        <textarea class="form-control" id="productDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveProduct()">
                    <i class="fas fa-save me-2"></i>
                    حفظ المنتج
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function editProduct(id) {
    // Find product data
    const products = <?= json_encode(DB::$products) ?>;
    const product = products.find(p => p.id === id);
    
    if (product) {
        alert(`تحرير المنتج: ${product.name}\n\nسيتم فتح نموذج التحرير مع البيانات المحملة مسبقاً:\n• الاسم: ${product.name}\n• السعر: ${product.wholesale_price} ر.س\n• المخزون: ${product.stock} قطعة`);
    }
}

function viewProduct(id) {
    const products = <?= json_encode(DB::$products) ?>;
    const product = products.find(p => p.id === id);
    
    if (product) {
        alert(`تفاصيل المنتج:\n\n• الاسم: ${product.name}\n• التصنيف: ${product.category}\n• السعر الأصلي: ${product.price} ر.س\n• سعر الجملة: ${product.wholesale_price} ر.س\n• المخزون: ${product.stock} قطعة\n• الحالة: نشط`);
    }
}

function deleteProduct(id) {
    const products = <?= json_encode(DB::$products) ?>;
    const product = products.find(p => p.id === id);
    
    if (product && confirm(`هل أنت متأكد من حذف المنتج "${product.name}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        alert('تم حذف المنتج بنجاح!\n\nفي النظام الحقيقي، سيتم حذف المنتج من قاعدة البيانات.');
    }
}

function saveProduct() {
    const name = document.getElementById('productName').value;
    const category = document.getElementById('productCategory').value;
    const price = document.getElementById('productPrice').value;
    const wholesalePrice = document.getElementById('wholesalePrice').value;
    const stock = document.getElementById('productStock').value;
    
    if (!name || !category || !price || !wholesalePrice || !stock) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    alert(`تم إضافة المنتج بنجاح!\n\nتفاصيل المنتج الجديد:\n• الاسم: ${name}\n• التصنيف: ${category}\n• السعر الأصلي: ${price} ر.س\n• سعر الجملة: ${wholesalePrice} ر.س\n• المخزون: ${stock} قطعة`);
    
    // Close modal and reset form
    const modal = bootstrap.Modal.getInstance(document.getElementById('addProductModal'));
    modal.hide();
    document.getElementById('addProductForm').reset();
}

// Auto-calculate wholesale price when original price changes
document.getElementById('productPrice').addEventListener('input', function() {
    const originalPrice = parseFloat(this.value);
    if (originalPrice) {
        const wholesalePrice = originalPrice * 0.85; // 15% discount
        document.getElementById('wholesalePrice').value = wholesalePrice.toFixed(2);
    }
});
</script>

<?php
$content = ob_get_clean();
include 'layout.php';
?>
