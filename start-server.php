<?php
// Simple server starter for Wholesale Marketplace

// Check if <PERSON><PERSON> is installed
if (!file_exists(__DIR__.'/vendor/autoload.php')) {
    echo "Laravel is not installed. Please run 'composer install' first.\n";
    echo "Opening installation guide...\n";
    
    // Open the installation page
    $url = 'http://localhost:8000';
    
    // Start PHP built-in server
    echo "Starting PHP server on $url\n";
    echo "Press Ctrl+C to stop the server\n\n";
    
    // Start server in background and open browser
    if (PHP_OS_FAMILY === 'Windows') {
        pclose(popen("start http://localhost:8000", "r"));
        passthru("php -S localhost:8000 -t public");
    } else {
        exec("php -S localhost:8000 -t public > /dev/null 2>&1 &");
        exec("open http://localhost:8000");
    }
} else {
    // Laravel is installed, start with artisan serve
    echo "Starting Laravel server...\n";
    echo "Opening http://localhost:8000\n";
    
    if (PHP_OS_FAMILY === 'Windows') {
        pclose(popen("start http://localhost:8000", "r"));
        passthru("php artisan serve");
    } else {
        exec("php artisan serve > /dev/null 2>&1 &");
        exec("open http://localhost:8000");
    }
}
?>
