@extends('layouts.admin')

@section('title', 'لوحة التحكم الرئيسية')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">لوحة التحكم الرئيسية</h1>
        <p class="text-muted">مرحباً {{ Auth::user()->name }}، إليك نظرة عامة على أداء المنصة</p>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row g-4 mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-users fa-3x mb-3"></i>
                <h3 class="fw-bold">{{ number_format($stats['total_users']) }}</h3>
                <p class="mb-0">إجمالي المستخدمين</p>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-box fa-3x mb-3"></i>
                <h3 class="fw-bold">{{ number_format($stats['total_products']) }}</h3>
                <p class="mb-0">إجمالي المنتجات</p>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-shopping-bag fa-3x mb-3"></i>
                <h3 class="fw-bold">{{ number_format($stats['total_orders']) }}</h3>
                <p class="mb-0">إجمالي الطلبات</p>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-dollar-sign fa-3x mb-3"></i>
                <h3 class="fw-bold">{{ number_format($stats['total_revenue']) }} ر.س</h3>
                <p class="mb-0">إجمالي الإيرادات</p>
            </div>
        </div>
    </div>
</div>

<!-- Additional Stats -->
<div class="row g-4 mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                <h4 class="text-warning">{{ number_format($stats['pending_orders']) }}</h4>
                <p class="mb-0">طلبات معلقة</p>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-store fa-2x text-success mb-2"></i>
                <h4 class="text-success">{{ number_format($stats['active_suppliers']) }}</h4>
                <p class="mb-0">موردين نشطين</p>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-user-friends fa-2x text-info mb-2"></i>
                <h4 class="text-info">{{ number_format($stats['active_retailers']) }}</h4>
                <p class="mb-0">تجار نشطين</p>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card border-danger">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                <h4 class="text-danger">{{ number_format($stats['low_stock_products']) }}</h4>
                <p class="mb-0">منتجات قليلة المخزون</p>
            </div>
        </div>
    </div>
</div>

<div class="row g-4">
    <!-- Recent Orders -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-bag me-2"></i>
                    أحدث الطلبات
                </h5>
            </div>
            <div class="card-body">
                @if($recentOrders->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentOrders as $order)
                                <tr>
                                    <td>
                                        <a href="{{ route('admin.orders.show', $order) }}" class="text-decoration-none">
                                            {{ $order->order_number }}
                                        </a>
                                    </td>
                                    <td>{{ $order->user->name }}</td>
                                    <td>{{ number_format($order->final_amount) }} ر.س</td>
                                    <td>
                                        @php
                                            $statusColors = [
                                                'pending' => 'warning',
                                                'processing' => 'info',
                                                'shipped' => 'primary',
                                                'delivered' => 'success',
                                                'cancelled' => 'danger'
                                            ];
                                            $statusNames = [
                                                'pending' => 'معلق',
                                                'processing' => 'قيد التجهيز',
                                                'shipped' => 'تم الشحن',
                                                'delivered' => 'تم التسليم',
                                                'cancelled' => 'ملغي'
                                            ];
                                        @endphp
                                        <span class="badge bg-{{ $statusColors[$order->status] ?? 'secondary' }} badge-status">
                                            {{ $statusNames[$order->status] ?? $order->status }}
                                        </span>
                                    </td>
                                    <td>{{ $order->created_at->format('Y-m-d') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد طلبات حتى الآن</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Top Products -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2"></i>
                    أكثر المنتجات مبيعاً
                </h5>
            </div>
            <div class="card-body">
                @if($topProducts->count() > 0)
                    @foreach($topProducts as $product)
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <img src="https://via.placeholder.com/50x50/0A2463/FFFFFF?text=P" 
                                 alt="{{ $product->name }}" 
                                 class="rounded" 
                                 width="50" 
                                 height="50">
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">{{ Str::limit($product->name, 30) }}</h6>
                            <small class="text-muted">
                                مبيع: {{ number_format($product->total_sold ?? 0) }} قطعة
                            </small>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="badge bg-primary">{{ number_format($product->wholesale_price) }} ر.س</span>
                        </div>
                    </div>
                    @endforeach
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-box fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مبيعات حتى الآن</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row g-4 mt-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    المبيعات الشهرية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="salesChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-friends me-2"></i>
                    أحدث المستخدمين
                </h5>
            </div>
            <div class="card-body">
                @if($recentUsers->count() > 0)
                    @foreach($recentUsers as $user)
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                 style="width: 40px; height: 40px;">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">{{ $user->name }}</h6>
                            <small class="text-muted">{{ $user->email }}</small>
                        </div>
                        <div class="flex-shrink-0">
                            <small class="text-muted">{{ $user->created_at->diffForHumans() }}</small>
                        </div>
                    </div>
                    @endforeach
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مستخدمين جدد</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Sales Chart
const ctx = document.getElementById('salesChart').getContext('2d');
const salesChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
        datasets: [{
            label: 'المبيعات (ر.س)',
            data: [
                @foreach(range(1, 12) as $month)
                    {{ $monthlySales->where('month', $month)->first()->total ?? 0 }},
                @endforeach
            ],
            borderColor: '#FF6B35',
            backgroundColor: 'rgba(255, 107, 53, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ر.س';
                    }
                }
            }
        }
    }
});
</script>
@endpush
