<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CartController extends Controller
{
    /**
     * Display the shopping cart.
     */
    public function index()
    {
        $cartItems = session('cart', []);
        $total = 0;
        $items = [];

        foreach ($cartItems as $item) {
            $product = Product::find($item['product_id']);
            if ($product) {
                $itemTotal = $product->wholesale_price * $item['quantity'];
                $total += $itemTotal;
                
                $items[] = [
                    'product' => $product,
                    'quantity' => $item['quantity'],
                    'color' => $item['color'] ?? null,
                    'size' => $item['size'] ?? null,
                    'total' => $itemTotal
                ];
            }
        }

        return view('cart.index', compact('items', 'total'));
    }

    /**
     * Add product to cart.
     */
    public function add(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'color' => 'nullable|string',
            'size' => 'nullable|string'
        ]);

        $product = Product::findOrFail($request->product_id);
        
        // Check stock
        if ($product->stock_quantity < $request->quantity) {
            return back()->with('error', 'الكمية المطلوبة غير متوفرة في المخزون');
        }

        // Check minimum order quantity
        if ($request->quantity < $product->min_order_quantity) {
            return back()->with('error', "الحد الأدنى للطلب هو {$product->min_order_quantity} قطعة");
        }

        $cart = session('cart', []);
        $cartKey = $request->product_id . '_' . ($request->color ?? '') . '_' . ($request->size ?? '');

        if (isset($cart[$cartKey])) {
            $cart[$cartKey]['quantity'] += $request->quantity;
        } else {
            $cart[$cartKey] = [
                'product_id' => $request->product_id,
                'quantity' => $request->quantity,
                'color' => $request->color,
                'size' => $request->size
            ];
        }

        session(['cart' => $cart]);

        return back()->with('success', 'تم إضافة المنتج إلى السلة بنجاح');
    }

    /**
     * Update cart item quantity.
     */
    public function update(Request $request)
    {
        $request->validate([
            'cart_key' => 'required|string',
            'quantity' => 'required|integer|min:1'
        ]);

        $cart = session('cart', []);
        
        if (isset($cart[$request->cart_key])) {
            $product = Product::find($cart[$request->cart_key]['product_id']);
            
            if ($product && $product->stock_quantity >= $request->quantity) {
                $cart[$request->cart_key]['quantity'] = $request->quantity;
                session(['cart' => $cart]);
                
                return response()->json([
                    'success' => true,
                    'message' => 'تم تحديث الكمية بنجاح'
                ]);
            }
        }

        return response()->json([
            'success' => false,
            'message' => 'حدث خطأ في تحديث الكمية'
        ]);
    }

    /**
     * Remove item from cart.
     */
    public function remove(Request $request)
    {
        $request->validate([
            'cart_key' => 'required|string'
        ]);

        $cart = session('cart', []);
        
        if (isset($cart[$request->cart_key])) {
            unset($cart[$request->cart_key]);
            session(['cart' => $cart]);
            
            return back()->with('success', 'تم حذف المنتج من السلة');
        }

        return back()->with('error', 'المنتج غير موجود في السلة');
    }

    /**
     * Clear entire cart.
     */
    public function clear()
    {
        session()->forget('cart');
        return back()->with('success', 'تم إفراغ السلة بنجاح');
    }

    /**
     * API: Add to cart (AJAX).
     */
    public function apiAdd(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'color' => 'nullable|string',
            'size' => 'nullable|string'
        ]);

        $product = Product::findOrFail($request->product_id);
        
        if ($product->stock_quantity < $request->quantity) {
            return response()->json([
                'success' => false,
                'message' => 'الكمية المطلوبة غير متوفرة في المخزون'
            ]);
        }

        $cart = session('cart', []);
        $cartKey = $request->product_id . '_' . ($request->color ?? '') . '_' . ($request->size ?? '');

        if (isset($cart[$cartKey])) {
            $cart[$cartKey]['quantity'] += $request->quantity;
        } else {
            $cart[$cartKey] = [
                'product_id' => $request->product_id,
                'quantity' => $request->quantity,
                'color' => $request->color,
                'size' => $request->size
            ];
        }

        session(['cart' => $cart]);

        return response()->json([
            'success' => true,
            'message' => 'تم إضافة المنتج إلى السلة بنجاح',
            'cart_count' => count($cart)
        ]);
    }

    /**
     * API: Remove from cart (AJAX).
     */
    public function apiRemove(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'color' => 'nullable|string',
            'size' => 'nullable|string'
        ]);

        $cart = session('cart', []);
        $cartKey = $request->product_id . '_' . ($request->color ?? '') . '_' . ($request->size ?? '');
        
        if (isset($cart[$cartKey])) {
            unset($cart[$cartKey]);
            session(['cart' => $cart]);
            
            return response()->json([
                'success' => true,
                'message' => 'تم حذف المنتج من السلة',
                'cart_count' => count($cart)
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'المنتج غير موجود في السلة'
        ]);
    }

    /**
     * API: Get cart count.
     */
    public function count()
    {
        $cart = session('cart', []);
        $totalItems = array_sum(array_column($cart, 'quantity'));
        
        return response()->json([
            'count' => $totalItems
        ]);
    }
}
