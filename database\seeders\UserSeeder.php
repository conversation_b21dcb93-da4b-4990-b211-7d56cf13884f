<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '0501234567',
            'company_name' => 'إدارة المنصة',
            'business_type' => 'retailer',
            'city' => 'الرياض',
            'country' => 'المملكة العربية السعودية',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
        $admin->assignRole('admin');

        // Create supplier user
        $supplier = User::create([
            'name' => 'أحمد محمد',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '0507654321',
            'company_name' => 'شركة التقنية المتقدمة',
            'business_type' => 'supplier',
            'address' => 'شارع الملك فهد، حي العليا',
            'city' => 'الرياض',
            'country' => 'المملكة العربية السعودية',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
        $supplier->assignRole('supplier');

        // Create retailer user
        $retailer = User::create([
            'name' => 'فاطمة علي',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '0551234567',
            'company_name' => 'متجر الأناقة',
            'business_type' => 'retailer',
            'address' => 'شارع التحلية، حي السليمانية',
            'city' => 'جدة',
            'country' => 'المملكة العربية السعودية',
            'is_active' => true,
            'loyalty_points' => 150,
            'email_verified_at' => now(),
        ]);
        $retailer->assignRole('retailer');

        // Create more sample users
        $users = [
            [
                'name' => 'خالد السعد',
                'email' => '<EMAIL>',
                'company_name' => 'مؤسسة النور التجارية',
                'business_type' => 'distributor',
                'city' => 'الدمام',
                'role' => 'retailer'
            ],
            [
                'name' => 'نورا أحمد',
                'email' => '<EMAIL>',
                'company_name' => 'شركة الإبداع للتجارة',
                'business_type' => 'retailer',
                'city' => 'مكة المكرمة',
                'role' => 'retailer'
            ],
            [
                'name' => 'محمد العتيبي',
                'email' => '<EMAIL>',
                'company_name' => 'مصنع الجودة العالية',
                'business_type' => 'manufacturer',
                'city' => 'الرياض',
                'role' => 'supplier'
            ]
        ];

        foreach ($users as $userData) {
            $user = User::create([
                'name' => $userData['name'],
                'email' => $userData['email'],
                'password' => Hash::make('password'),
                'phone' => '050' . rand(1000000, 9999999),
                'company_name' => $userData['company_name'],
                'business_type' => $userData['business_type'],
                'city' => $userData['city'],
                'country' => 'المملكة العربية السعودية',
                'is_active' => true,
                'loyalty_points' => rand(0, 500),
                'email_verified_at' => now(),
            ]);
            $user->assignRole($userData['role']);
        }
    }
}
