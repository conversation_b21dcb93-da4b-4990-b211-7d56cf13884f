<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use App\Models\User;
use App\Models\Order;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Show the application homepage.
     */
    public function index()
    {
        // Get featured products
        $featuredProducts = Product::with(['category', 'supplier', 'reviews'])
            ->active()
            ->featured()
            ->take(8)
            ->get();

        // Get popular categories
        $popularCategories = Category::withCount('products')
            ->active()
            ->parent()
            ->orderBy('products_count', 'desc')
            ->take(6)
            ->get();

        // Get latest products
        $latestProducts = Product::with(['category', 'supplier'])
            ->active()
            ->latest()
            ->take(8)
            ->get();

        // Get statistics
        $stats = [
            'total_products' => Product::active()->count(),
            'total_suppliers' => User::role('supplier')->where('is_active', true)->count(),
            'total_orders' => Order::count(),
            'total_customers' => User::role('retailer')->where('is_active', true)->count(),
        ];

        return view('home', compact(
            'featuredProducts',
            'popularCategories', 
            'latestProducts',
            'stats'
        ));
    }
}
